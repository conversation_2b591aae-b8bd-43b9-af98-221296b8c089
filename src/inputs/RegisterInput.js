const BaseInput = require('./BaseInput');

/**
 * RegisterInput class handles validation for user registration requests.
 * Extends BaseInput to provide registration-specific validation schema.
 */
class RegisterInput extends BaseInput {
  /**
   * Defines the validation schema for user registration requests.
   * @returns {Object} JSON schema object for registration validation
   */
  getSchema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 2,
          maxLength: 100,
          pattern: '^[a-zA-Z\\s]+$',
        },
        email: {
          type: 'string',
          format: 'email',
          minLength: 1,
          maxLength: 255,
        },
        password: {
          type: 'string',
          minLength: 8,
          maxLength: 255,
          pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
        },
        role: {
          type: 'string',
          enum: ['admin', 'user', 'moderator'],
          default: 'user',
        },
      },
      required: ['name', 'email', 'password'],
      additionalProperties: false,
    };
  }

  /**
   * Static method to create a new RegisterInput instance with data.
   * @param {Object} data - The data to validate
   * @returns {RegisterInput} RegisterInput instance
   */
  static new(data) {
    return new RegisterInput(data);
  }
}

module.exports = RegisterInput;
