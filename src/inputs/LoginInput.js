const BaseInput = require('./BaseInput');

/**
 * LoginInput class handles validation for user login requests.
 * Extends BaseInput to provide login-specific validation schema.
 */
class LoginInput extends BaseInput {
  /**
   * Defines the validation schema for login requests.
   * @returns {Object} JSON schema object for login validation
   */
  getSchema() {
    return {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          minLength: 1,
          maxLength: 255,
        },
        password: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
        },
      },
      required: ['email', 'password'],
      additionalProperties: false,
    };
  }

  /**
   * Static method to create a new LoginInput instance with data.
   * @param {Object} data - The data to validate
   * @returns {LoginInput} LoginInput instance
   */
  static new(data) {
    return new LoginInput(data);
  }
}

module.exports = LoginInput;
