const Ajv = require('ajv');
const addFormats = require('ajv-formats');

/**
 * BaseInput class provides common validation functionality for all input validation schemas.
 * This class serves as the parent class for all endpoint-specific input validation classes.
 */
class BaseInput {
  constructor(data = null) {
    // Initialize AJV with common configuration
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);

    // Compile the schema when the class is instantiated
    this.validator = this.ajv.compile(this.getSchema());

    // Store the input data and initialize validation state
    this.data = data;
    this.validationResult = null;
    this.output = null;
  }

  /**
   * Abstract method to be implemented by child classes.
   * Each child class should define its own validation schema.
   * @returns {Object} The JSON schema object for validation
   * @throws {Error} If not implemented by child class
   */
  getSchema() {
    throw new Error('getSchema() must be implemented by child classes');
  }

  /**
   * Validates the data against the schema.
   * If no data is provided, uses the data from constructor.
   * @param {Object} data - Optional data to validate (uses this.data if not provided)
   * @returns {Object} Validation result with success flag and errors
   */
  validate(data = null) {
    const dataToValidate = data || this.data;

    if (!dataToValidate) {
      throw new Error('No data provided for validation');
    }

    const isValid = this.validator(dataToValidate);

    this.validationResult = {
      success: isValid,
      errors: isValid ? null : this.formatErrors(this.validator.errors),
    };

    // If validation is successful, store the validated data in output
    if (isValid) {
      this.output = dataToValidate;
    }

    return this.validationResult;
  }

  /**
   * Formats AJV validation errors into a more user-friendly format.
   * @param {Array} ajvErrors - Array of AJV error objects
   * @returns {Object} Formatted errors object with field names as keys
   */
  formatErrors(ajvErrors) {
    const errors = {};
    
    ajvErrors.forEach((error) => {
      const field = error.instancePath.replace('/', '') || error.params?.missingProperty;
      if (field) {
        errors[field] = error.message;
      } else {
        errors.general = error.message;
      }
    });

    return errors;
  }

  /**
   * Creates an Express middleware function for validation.
   * This middleware validates req.body against the schema and returns
   * a 400 error response if validation fails.
   * @returns {Function} Express middleware function
   */
  getMiddleware() {
    return (req, res, next) => {
      const validationResult = this.validate(req.body);

      if (!validationResult.success) {
        return res.status(400).json({
          error: 'Validation Error',
          message: 'Invalid input data',
          errors: validationResult.errors,
          timestamp: new Date().toISOString(),
        });
      }

      next();
    };
  }

  /**
   * Static method to create a middleware instance for a specific input class.
   * This is a convenience method for creating middleware without instantiating the class manually.
   * @param {Class} InputClass - The input validation class to use
   * @returns {Function} Express middleware function
   */
  static createMiddleware(InputClass) {
    const inputInstance = new InputClass();
    return inputInstance.getMiddleware();
  }

  /**
   * Static method to create a new instance with data.
   * This is a convenience method for the new controller-based pattern.
   * @param {Class} InputClass - The input validation class to use
   * @param {Object} data - The data to validate
   * @returns {Object} Input instance with data
   */
  static new(InputClass, data) {
    return new InputClass(data);
  }
}

module.exports = BaseInput;
