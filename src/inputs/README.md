# Input Validation Architecture

This directory contains the class-based input validation system for the Express API. The architecture provides a clean, maintainable way to handle input validation for different endpoints.

## Architecture Overview

### BaseInput Class
The `BaseInput` class serves as the parent class for all input validation schemas. It provides:

- **AJV Setup**: Configures AJV (Another JSON Schema Validator) with common settings
- **Schema Compilation**: Automatically compiles validation schemas when classes are instantiated
- **Error Formatting**: Converts AJV errors into user-friendly format
- **Middleware Creation**: Generates Express middleware functions for validation
- **Static Helper Methods**: Convenience methods for creating middleware instances

### Child Classes
Each endpoint that requires input validation has its own dedicated class that extends `BaseInput`:

- **LoginInput**: Validates login requests (email, password)
- **RegisterInput**: Validates user registration requests (name, email, password, role)

## Usage

### Creating a New Input Validation Class

1. Create a new class that extends `BaseInput`
2. Implement the `getSchema()` method to return your JSON schema
3. Export the class

```javascript
const BaseInput = require('./BaseInput');

class MyEndpointInput extends BaseInput {
  getSchema() {
    return {
      type: 'object',
      properties: {
        field1: {
          type: 'string',
          minLength: 1,
          maxLength: 100,
        },
        field2: {
          type: 'number',
          minimum: 0,
        },
      },
      required: ['field1'],
      additionalProperties: false,
    };
  }
}

module.exports = MyEndpointInput;
```

### Using in Routes

There are two ways to use the validation classes in your routes:

#### Method 1: Using Static Helper (Recommended)
```javascript
const BaseInput = require('../inputs/BaseInput');
const MyEndpointInput = require('../inputs/MyEndpointInput');

const validateMiddleware = BaseInput.createMiddleware(MyEndpointInput);
router.post('/my-endpoint', validateMiddleware, MyController.myMethod);
```

#### Method 2: Manual Instantiation
```javascript
const MyEndpointInput = require('../inputs/MyEndpointInput');

const myInput = new MyEndpointInput();
const validateMiddleware = myInput.getMiddleware();
router.post('/my-endpoint', validateMiddleware, MyController.myMethod);
```

### Direct Validation (for testing or custom logic)
```javascript
const MyEndpointInput = require('../inputs/MyEndpointInput');

const myInput = new MyEndpointInput();
const result = myInput.validate(requestData);

if (result.success) {
  // Data is valid
  console.log('Validation passed');
} else {
  // Data is invalid
  console.log('Validation errors:', result.errors);
}
```

## Error Response Format

When validation fails, the middleware returns a standardized error response:

```json
{
  "error": "Validation Error",
  "message": "Invalid input data",
  "errors": {
    "fieldName": "error message",
    "anotherField": "another error message"
  },
  "timestamp": "2023-12-07T10:30:00.000Z"
}
```

## Benefits

1. **Separation of Concerns**: Each endpoint has its own validation logic
2. **Reusability**: Common validation functionality is inherited from BaseInput
3. **Maintainability**: Easy to modify validation rules for specific endpoints
4. **Testability**: Each validation class can be unit tested independently
5. **Consistency**: All validation follows the same patterns and error formats
6. **Extensibility**: Easy to add new validation classes for new endpoints

## Testing

The validation classes are fully unit tested. See `tests/validation.test.js` for examples of how to test validation logic.

To run validation tests:
```bash
npm test -- tests/validation.test.js
```

## Migration from Old System

The old `authSchemas.js` file has been replaced with this class-based system. The functionality remains the same, but the architecture is now more maintainable and extensible.

### Key Changes:
- Replaced function-based validation with class-based validation
- Each endpoint now has its own dedicated validation class
- Common functionality is shared through inheritance
- Improved testability and maintainability
