const { sequelize } = require('../config/sequelize');
const User = require('./User');

// Initialize all models
const models = {
  User,
};

// Set up associations here if needed
// Example: User.hasMany(models.Post);

// Sync all models with database (only in development)
const syncDatabase = async () => {
  if (process.env.NODE_ENV === 'development') {
    try {
      await sequelize.sync({ alter: true });
      console.log('Database synchronized successfully.');
    } catch (error) {
      console.error('Error synchronizing database:', error);
    }
  }
};

module.exports = {
  sequelize,
  models,
  syncDatabase,
  ...models,
};
