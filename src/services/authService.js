const jwt = require('jsonwebtoken');
const User = require('../models/User');

class AuthService {
  /**
   * Generate JWT token for user
   * @param {Object} user - User object
   * @returns {string} JWT token
   */
  static generateToken(user) {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    const options = {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      issuer: 'simple-express-api',
      audience: 'simple-express-api-users',
    };

    return jwt.sign(payload, process.env.JWT_SECRET, options);
  }

  /**
   * Verify JWT token
   * @param {string} token - JWT token
   * @returns {Object} Decoded token payload
   */
  static verifyToken(token) {
    const options = {
      issuer: 'simple-express-api',
      audience: 'simple-express-api-users',
    };

    return jwt.verify(token, process.env.JWT_SECRET, options);
  }

  /**
   * Authenticate user with email and password
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} Authentication result
   */
  static async authenticateUser(email, password) {
    try {
      // Find user by email
      const user = await User.findByEmail(email);

      if (!user) {
        return {
          success: false,
          message: 'Invalid email or password',
          statusCode: 401,
        };
      }

      // Verify password
      const isPasswordValid = await user.verifyPassword(password);

      if (!isPasswordValid) {
        return {
          success: false,
          message: 'Invalid email or password',
          statusCode: 401,
        };
      }

      // Generate token
      const token = this.generateToken(user);

      return {
        success: true,
        message: 'Authentication successful',
        data: {
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            created_at: user.created_at,
          },
        },
        statusCode: 200,
      };
    } catch (error) {
      console.error('Authentication error:', error);
      return {
        success: false,
        message: 'Authentication failed',
        statusCode: 500,
      };
    }
  }

  /**
   * Create a new user (for future registration endpoint)
   * @param {Object} userData - User data
   * @returns {Object} User creation result
   */
  static async createUser(userData) {
    try {
      const { name, email, password, role = 'user' } = userData;

      // Check if user already exists
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return {
          success: false,
          message: 'User with this email already exists',
          statusCode: 409,
        };
      }

      // Hash password
      const password_digest = await User.hashPassword(password);

      // Create user
      const user = await User.create({
        name,
        email,
        password_digest,
        role,
      });

      // Generate token
      const token = this.generateToken(user);

      return {
        success: true,
        message: 'User created successfully',
        data: {
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            created_at: user.created_at,
          },
        },
        statusCode: 201,
      };
    } catch (error) {
      console.error('User creation error:', error);
      return {
        success: false,
        message: 'Failed to create user',
        statusCode: 500,
      };
    }
  }
}

module.exports = AuthService;
