const requestLogger = (req, res, next) => {
  const startTime = Date.now();

  // Log request details
  const logData = {
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    timestamp: new Date().toISOString(),
  };

  // Don't log sensitive data
  if (req.body && Object.keys(req.body).length > 0) {
    const sanitizedBody = { ...req.body };

    // Remove password fields from logs
    if (sanitizedBody.password) {
      sanitizedBody.password = '[REDACTED]';
    }
    if (sanitizedBody.password_confirmation) {
      sanitizedBody.password_confirmation = '[REDACTED]';
    }

    logData.body = sanitizedBody;
  }

  console.log('📥 Incoming Request:', logData);

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function (data) {
    const duration = Date.now() - startTime;

    console.log('📤 Outgoing Response:', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
    });

    return originalJson.call(this, data);
  };

  next();
};

module.exports = requestLogger;
