const errorHandler = (err, req, res, next) => {
  // Log the error
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
  });

  // Default error response
  let statusCode = 500;
  let message = 'Internal Server Error';
  let errors = null;

  // Handle different types of errors
  if (err.name === 'ValidationError') {
    // Sequelize validation errors
    statusCode = 400;
    message = 'Validation Error';
    errors = {};

    if (err.errors) {
      err.errors.forEach((error) => {
        errors[error.path] = error.message;
      });
    }
  } else if (err.name === 'SequelizeUniqueConstraintError') {
    // Sequelize unique constraint errors
    statusCode = 409;
    message = 'Duplicate Entry';
    errors = {};

    if (err.errors) {
      err.errors.forEach((error) => {
        errors[error.path] = `${error.path} already exists`;
      });
    }
  } else if (err.name === 'SequelizeDatabaseError') {
    // Database errors
    statusCode = 500;
    message = 'Database Error';
  } else if (err.name === 'JsonWebTokenError') {
    // JWT errors
    statusCode = 401;
    message = 'Invalid token';
  } else if (err.name === 'TokenExpiredError') {
    // JWT expired errors
    statusCode = 401;
    message = 'Token expired';
  } else if (err.statusCode || err.status) {
    // Custom errors with status codes
    statusCode = err.statusCode || err.status;
    message = err.message;
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Something went wrong';
  }

  // Send error response
  const errorResponse = {
    error: message,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  if (errors) {
    errorResponse.errors = errors;
  }

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
  }

  res.status(statusCode).json(errorResponse);
};

module.exports = errorHandler;
