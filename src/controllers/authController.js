const BaseController = require('./BaseController');
const AuthService = require('../services/authService');
const LoginInput = require('../inputs/LoginInput');
const RegisterInput = require('../inputs/RegisterInput');
const { LoginOutput, RegisterOutput, TokenVerificationOutput, LogoutOutput, AuthErrorOutput } = require('../outputs/authResponses');

class AuthController extends BaseController {
  /**
   * Handle user login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async login(req, res, next) {
    try {
      // Validate input
      const input = LoginInput.new(req.body);
      if (!this.validateInput(input, res)) {
        return;
      }

      // Authenticate user using validated input
      const result = await AuthService.authenticateUser(input.output.email, input.output.password);

      if (!result.success) {
        const errorOutput = new AuthErrorOutput(result.message, { status: result.statusCode });
        return this.sendResponse(errorOutput, res);
      }

      // Success response
      const output = new LoginOutput(result.data, { status: result.statusCode });
      this.sendResponse(output, res);
    } catch (error) {
      this.handleError(error, res, next);
    }
  }

  /**
   * Handle user registration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async register(req, res, next) {
    try {
      // Validate input
      const input = RegisterInput.new(req.body);
      if (!this.validateInput(input, res)) {
        return;
      }

      // Create user using validated input
      const result = await AuthService.createUser(input.output);

      if (!result.success) {
        const errorOutput = new AuthErrorOutput(result.message, { status: result.statusCode });
        return this.sendResponse(errorOutput, res);
      }

      // Success response
      const output = new RegisterOutput(result.data, { status: result.statusCode });
      this.sendResponse(output, res);
    } catch (error) {
      this.handleError(error, res, next);
    }
  }

  /**
   * Handle token verification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async verifyToken(req, res, next) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        const errorOutput = new AuthErrorOutput('No token provided', { status: 401 });
        return this.sendResponse(errorOutput, res);
      }

      const decoded = AuthService.verifyToken(token);
      const output = new TokenVerificationOutput(decoded);
      this.sendResponse(output, res);
    } catch (error) {
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        const errorOutput = new AuthErrorOutput('Invalid or expired token', { status: 401 });
        return this.sendResponse(errorOutput, res);
      }
      this.handleError(error, res, next);
    }
  }

  /**
   * Handle user logout (for future use - token blacklisting)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async logout(req, res, next) {
    try {
      // In a real application, you might want to blacklist the token
      // For now, we'll just return a success message
      const output = new LogoutOutput();
      this.sendResponse(output, res);
    } catch (error) {
      this.handleError(error, res, next);
    }
  }
}

module.exports = AuthController;
