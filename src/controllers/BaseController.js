/**
 * BaseController class provides common functionality for all controllers.
 * This class serves as the parent class for all endpoint-specific controller classes.
 */
class BaseController {
  /**
   * Handle input validation and return formatted error response if validation fails.
   * @param {Object} input - Input instance with validation capability
   * @param {Object} res - Express response object
   * @returns {boolean} True if validation passed, false if validation failed (and response was sent)
   */
  static validateInput(input, res) {
    const validationResult = input.validate();
    
    if (!validationResult.success) {
      res.status(400).json({
        error: 'Validation Error',
        message: 'Invalid input data',
        errors: validationResult.errors,
        timestamp: new Date().toISOString(),
      });
      return false;
    }
    
    return true;
  }

  /**
   * Send a formatted response using an output instance.
   * @param {Object} output - Output instance with formatting capability
   * @param {Object} res - Express response object
   */
  static sendResponse(output, res) {
    res.status(output.statusCode).json(output.format());
  }

  /**
   * Handle errors in a consistent way across all controllers.
   * @param {Error} error - The error object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static handleError(error, res, next) {
    console.error('Controller error:', error);
    
    // If response has already been sent, pass to error handler middleware
    if (res.headersSent) {
      return next(error);
    }
    
    // Send generic error response
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Wrapper method to handle controller actions with consistent error handling.
   * @param {Function} action - The controller action function
   * @returns {Function} Express middleware function
   */
  static async handleAction(action) {
    return async (req, res, next) => {
      try {
        await action(req, res, next);
      } catch (error) {
        this.handleError(error, res, next);
      }
    };
  }
}

module.exports = BaseController;
