const ErrorOutput = require('../outputs/ErrorOutput');

/**
 * BaseController class provides common functionality for all controllers.
 * This class serves as the parent class for all endpoint-specific controller classes.
 */
class BaseController {
  /**
   * Handle input validation and return formatted error response if validation fails.
   * @param {Object} input - Input instance with validation capability
   * @param {Object} res - Express response object
   * @returns {boolean} True if validation passed, false if validation failed (and response was sent)
   */
  static validateInput(input, res) {
    const validationResult = input.validate();

    if (!validationResult.success) {
      const errorOutput = ErrorOutput.fromValidation(validationResult, {
        path: res.req?.path,
        method: res.req?.method
      });
      this.sendResponse(errorOutput, res);
      return false;
    }

    return true;
  }

  /**
   * Send a formatted response using an output instance.
   * @param {Object} output - Output instance with formatting capability
   * @param {Object} res - Express response object
   */
  static sendResponse(output, res) {
    res.status(output.statusCode).json(output.format());
  }

  /**
   * Handle errors in a consistent way across all controllers.
   * @param {Error} error - The error object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static handleError(error, res, next) {
    console.error('Controller error:', error);

    // If response has already been sent, pass to error handler middleware
    if (res.headersSent) {
      return next(error);
    }

    // Create standardized error response
    const errorOutput = new ErrorOutput(error, {
      path: res.req?.path,
      method: res.req?.method
    });

    this.sendResponse(errorOutput, res);
  }

  /**
   * Wrapper method to handle controller actions with consistent error handling.
   * This method automatically wraps controller actions in try-catch blocks.
   * @param {Function} action - The controller action function
   * @returns {Function} Express middleware function
   */
  static handleAction(action) {
    return async (req, res, next) => {
      try {
        await action.call(this, req, res, next);
      } catch (error) {
        this.handleError(error, res, next);
      }
    };
  }

  /**
   * Create a controller method that automatically handles errors.
   * This eliminates the need for try-catch blocks in individual controller methods.
   * @param {Function} method - The controller method to wrap
   * @returns {Function} Wrapped controller method
   */
  static createMethod(method) {
    return this.handleAction(method);
  }
}

module.exports = BaseController;
