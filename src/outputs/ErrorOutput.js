const BaseOutput = require('./BaseOutput');

/**
 * ErrorOutput class provides standardized error response formatting.
 * This ensures all errors across the application have consistent structure.
 */
class ErrorOutput extends BaseOutput {
  /**
   * Constructor for ErrorOutput
   * @param {string|Error} error - Error message or Error object
   * @param {Object} options - Options for the error response
   * @param {number} options.status - HTTP status code (default: 500)
   * @param {string} options.format - Format type (default: 'json')
   * @param {Object} options.errors - Validation errors object
   * @param {string} options.path - Request path where error occurred
   * @param {string} options.method - HTTP method where error occurred
   */
  constructor(error, options = {}) {
    // Extract error message
    const message = error instanceof Error ? error.message : error;
    
    // Set default status based on error type
    let defaultStatus = 500;
    if (error instanceof Error) {
      if (error.name === 'ValidationError') defaultStatus = 400;
      else if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') defaultStatus = 401;
      else if (error.name === 'SequelizeUniqueConstraintError') defaultStatus = 409;
      else if (error.statusCode || error.status) defaultStatus = error.statusCode || error.status;
    }

    super(null, {
      status: options.status || defaultStatus,
      message: message || 'An error occurred',
      format: options.format || 'json',
      ...options
    });

    this.errors = options.errors || null;
    this.path = options.path || null;
    this.method = options.method || null;
    this.originalError = error instanceof Error ? error : null;
  }

  /**
   * Format the error response
   * @returns {Object} Formatted error response
   */
  format() {
    const response = {
      error: this.message,
      timestamp: this.timestamp,
    };

    // Add validation errors if present
    if (this.errors) {
      response.errors = this.errors;
    }

    // Add request context if available
    if (this.path) {
      response.path = this.path;
    }

    if (this.method) {
      response.method = this.method;
    }

    // Include stack trace in development
    if (process.env.NODE_ENV === 'development' && this.originalError && this.originalError.stack) {
      response.stack = this.originalError.stack;
    }

    return response;
  }

  /**
   * Format as plain format for errors
   * @returns {Object} Plain formatted error response
   */
  formatPlain() {
    return {
      success: false,
      error: this.message,
      errors: this.errors,
      timestamp: this.timestamp,
    };
  }

  /**
   * Format as detailed format for errors
   * @returns {Object} Detailed formatted error response
   */
  formatDetailed() {
    const response = this.format();
    
    response.status = {
      code: this.statusCode,
      success: false,
    };

    return response;
  }

  /**
   * Static method to create error from validation result
   * @param {Object} validationResult - Validation result with errors
   * @param {Object} options - Additional options
   * @returns {ErrorOutput} ErrorOutput instance
   */
  static fromValidation(validationResult, options = {}) {
    return new ErrorOutput('Validation Error', {
      status: 400,
      errors: validationResult.errors,
      ...options
    });
  }

  /**
   * Static method to create error from service result
   * @param {Object} serviceResult - Service result with error information
   * @param {Object} options - Additional options
   * @returns {ErrorOutput} ErrorOutput instance
   */
  static fromServiceResult(serviceResult, options = {}) {
    return new ErrorOutput(serviceResult.message, {
      status: serviceResult.statusCode || 500,
      ...options
    });
  }

  /**
   * Static method to create authentication error
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   * @returns {ErrorOutput} ErrorOutput instance
   */
  static authError(message = 'Authentication failed', options = {}) {
    return new ErrorOutput(message, {
      status: 401,
      ...options
    });
  }

  /**
   * Static method to create authorization error
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   * @returns {ErrorOutput} ErrorOutput instance
   */
  static authorizationError(message = 'Access denied', options = {}) {
    return new ErrorOutput(message, {
      status: 403,
      ...options
    });
  }

  /**
   * Static method to create not found error
   * @param {string} message - Error message
   * @param {Object} options - Additional options
   * @returns {ErrorOutput} ErrorOutput instance
   */
  static notFound(message = 'Resource not found', options = {}) {
    return new ErrorOutput(message, {
      status: 404,
      ...options
    });
  }
}

module.exports = ErrorOutput;
