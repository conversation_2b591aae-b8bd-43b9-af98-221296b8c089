const BaseOutput = require('./BaseOutput');





/**
 * AuthOutput class with different format options
 * Handles both login and register responses with flexible formatting
 */
class AuthOutput extends BaseOutput {
  constructor(result, options = {}) {
    // Determine default status and message based on format type
    let defaultStatus = 200;
    let defaultMessage = 'Authentication successful';

    if (options.formatType === 'register' || options.formatType === 'registerFormat') {
      defaultStatus = 201;
      defaultMessage = 'Registration successful';
    } else if (options.formatType === 'login' || options.formatType === 'loginFormat') {
      defaultStatus = 200;
      defaultMessage = 'Login successful';
    }

    // Format user data
    const data = {
      token: result.token,
      user: AuthOutput.formatUser(result.user),
    };

    super(data, {
      status: options.status || defaultStatus,
      message: options.message || defaultMessage,
      format: options.format || 'json',
      ...options
    });

    // Store the format type separately
    this.formatType = options.formatType || 'login';
  }

  /**
   * Format response based on the specified format type
   * @returns {Object} Formatted response
   */
  format() {
    switch (this.formatType) {
      case 'login':
      case 'loginFormat':
        return this.loginFormat();
      case 'register':
      case 'registerFormat':
        return this.registerFormat();
      default:
        return this.getFormattedResponse();
    }
  }

  /**
   * Login-specific format
   * @returns {Object} Login formatted response
   */
  loginFormat() {
    const baseResponse = this.getFormattedResponse();

    // Add login-specific metadata
    baseResponse.meta = {
      ...(baseResponse.meta || {}),
      action: 'login'
    };

    return baseResponse;
  }

  /**
   * Register-specific format
   * @returns {Object} Register formatted response
   */
  registerFormat() {
    const baseResponse = this.getFormattedResponse();

    // Add registration-specific metadata
    baseResponse.meta = {
      ...(baseResponse.meta || {}),
      action: 'register',
      welcome: true
    };

    return baseResponse;
  }

  /**
   * Format user data for response
   * @param {Object} user - User object
   * @returns {Object} Formatted user data
   */
  static formatUser(user) {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      created_at: user.created_at,
    };
  }

  /**
   * Static method to create login response
   * @param {Object} result - Service result
   * @param {Object} options - Additional options
   * @returns {AuthOutput} AuthOutput instance for login
   */
  static login(result, options = {}) {
    return new AuthOutput(result, {
      formatType: 'login',
      ...options
    });
  }

  /**
   * Static method to create register response
   * @param {Object} result - Service result
   * @param {Object} options - Additional options
   * @returns {AuthOutput} AuthOutput instance for registration
   */
  static register(result, options = {}) {
    return new AuthOutput(result, {
      formatType: 'register',
      ...options
    });
  }
}



class TokenVerificationOutput extends AuthOutput {
  constructor(decoded, options = {}) {
    const data = {
      user: {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
      },
    };

    super(data, {
      status: options.status || 200,
      message: options.message || 'Token is valid',
      format: options.format || 'json',
      ...options
    });
  }
}

class LogoutOutput extends AuthOutput {
  constructor(options = {}) {
    super(null, {
      status: options.status || 200,
      message: options.message || 'Logout successful',
      format: options.format || 'json',
      ...options
    });
  }
}

class AuthErrorOutput extends BaseOutput {
  constructor(message, options = {}) {
    super(null, {
      status: options.status || 401,
      message: message || 'Authentication failed',
      format: options.format || 'json',
      ...options
    });
  }

  format() {
    return {
      error: this.message,
      timestamp: this.timestamp,
    };
  }
}

module.exports = {
  AuthOutput,
  TokenVerificationOutput,
  LogoutOutput,
  AuthErrorOutput,
};
