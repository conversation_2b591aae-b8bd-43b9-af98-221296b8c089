const BaseOutput = require('./BaseOutput');

/**
 * Authentication response schemas and formatters
 */
class AuthOutput extends BaseOutput {
  /**
   * Constructor for AuthOutput
   * @param {*} data - The data to be formatted in the response
   * @param {Object} options - Options for the response
   */
  constructor(data, options = {}) {
    super(data, options);
  }

  /**
   * Format user data for responses (removes sensitive information)
   * @param {Object} user - User object
   * @returns {Object} Formatted user object
   */
  static formatUser(user) {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      created_at: user.created_at,
    };
  }
}

/**
 * Legacy AuthResponses class for backward compatibility
 * @deprecated Use AuthOutput instead
 */
class AuthResponses {
  /**
   * Format login success response
   * @param {Object} user - User object
   * @param {string} token - JWT token
   * @returns {Object} Formatted response
   */
  static loginSuccess(user, token) {
    return {
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          created_at: user.created_at,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format registration success response
   * @param {Object} user - User object
   * @param {string} token - JWT token
   * @returns {Object} Formatted response
   */
  static registrationSuccess(user, token) {
    return {
      message: 'Registration successful',
      data: {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          created_at: user.created_at,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format token verification success response
   * @param {Object} decoded - Decoded token payload
   * @returns {Object} Formatted response
   */
  static tokenVerificationSuccess(decoded) {
    return {
      message: 'Token is valid',
      data: {
        user: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format logout success response
   * @returns {Object} Formatted response
   */
  static logoutSuccess() {
    return {
      message: 'Logout successful',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format authentication error response
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static authenticationError(message = 'Authentication failed') {
    return {
      error: message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format validation error response
   * @param {Object} errors - Validation errors
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static validationError(errors, message = 'Validation Error') {
    return {
      error: message,
      errors,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format token error response
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static tokenError(message = 'Invalid or expired token') {
    return {
      error: message,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Unified AuthOutput class with different format options
 * Replaces LoginOutput and RegisterOutput with a single flexible class
 */
class UnifiedAuthOutput extends AuthOutput {
  constructor(result, options = {}) {
    const data = {
      token: result.token,
      user: AuthOutput.formatUser(result.user),
    };

    // Determine default status and message based on format type
    let defaultStatus = 200;
    let defaultMessage = 'Authentication successful';

    if (options.formatType === 'register' || options.formatType === 'registerFormat') {
      defaultStatus = 201;
      defaultMessage = 'Registration successful';
    } else if (options.formatType === 'login' || options.formatType === 'loginFormat') {
      defaultStatus = 200;
      defaultMessage = 'Login successful';
    }

    super(data, {
      status: options.status || defaultStatus,
      message: options.message || defaultMessage,
      format: options.format || 'json',
      ...options
    });

    // Store the format type separately
    this.formatType = options.formatType || 'login';
  }

  /**
   * Format response based on the specified format type
   * @returns {Object} Formatted response
   */
  format() {
    switch (this.formatType) {
      case 'login':
      case 'loginFormat':
        return this.loginFormat();
      case 'register':
      case 'registerFormat':
        return this.registerFormat();
      default:
        return this.getFormattedResponse();
    }
  }

  /**
   * Login-specific format
   * @returns {Object} Login formatted response
   */
  loginFormat() {
    const baseResponse = this.getFormattedResponse();

    // Add login-specific metadata
    baseResponse.meta = {
      ...(baseResponse.meta || {}),
      action: 'login'
    };

    return baseResponse;
  }

  /**
   * Register-specific format
   * @returns {Object} Register formatted response
   */
  registerFormat() {
    const baseResponse = this.getFormattedResponse();

    // Add registration-specific metadata
    baseResponse.meta = {
      ...(baseResponse.meta || {}),
      action: 'register',
      welcome: true
    };

    return baseResponse;
  }

  /**
   * Static method to create login response
   * @param {Object} result - Service result
   * @param {Object} options - Additional options
   * @returns {UnifiedAuthOutput} AuthOutput instance for login
   */
  static login(result, options = {}) {
    return new UnifiedAuthOutput(result, {
      formatType: 'login',
      ...options
    });
  }

  /**
   * Static method to create register response
   * @param {Object} result - Service result
   * @param {Object} options - Additional options
   * @returns {UnifiedAuthOutput} AuthOutput instance for registration
   */
  static register(result, options = {}) {
    return new UnifiedAuthOutput(result, {
      formatType: 'register',
      ...options
    });
  }
}

// Keep original classes for backward compatibility
class LoginOutput extends UnifiedAuthOutput {
  constructor(result, options = {}) {
    super(result, { formatType: 'login', ...options });
  }
}

class RegisterOutput extends UnifiedAuthOutput {
  constructor(result, options = {}) {
    super(result, { formatType: 'register', ...options });
  }
}

class TokenVerificationOutput extends AuthOutput {
  constructor(decoded, options = {}) {
    const data = {
      user: {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
      },
    };

    super(data, {
      status: options.status || 200,
      message: options.message || 'Token is valid',
      format: options.format || 'json',
      ...options
    });
  }
}

class LogoutOutput extends AuthOutput {
  constructor(options = {}) {
    super(null, {
      status: options.status || 200,
      message: options.message || 'Logout successful',
      format: options.format || 'json',
      ...options
    });
  }
}

class AuthErrorOutput extends AuthOutput {
  constructor(message, options = {}) {
    super(null, {
      status: options.status || 401,
      message: message || 'Authentication failed',
      format: options.format || 'json',
      ...options
    });
  }

  format() {
    return {
      error: this.message,
      timestamp: this.timestamp,
    };
  }
}

module.exports = {
  AuthResponses, // Legacy class for backward compatibility
  AuthOutput,
  UnifiedAuthOutput, // New unified class
  LoginOutput, // Backward compatibility
  RegisterOutput, // Backward compatibility
  TokenVerificationOutput,
  LogoutOutput,
  AuthErrorOutput,
};
