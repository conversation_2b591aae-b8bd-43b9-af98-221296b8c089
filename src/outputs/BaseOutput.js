/**
 * BaseOutput class provides common functionality for all output formatting.
 * This class serves as the parent class for all endpoint-specific output classes.
 */
class BaseOutput {
  /**
   * Constructor for BaseOutput
   * @param {*} data - The data to be formatted in the response
   * @param {Object} options - Options for the response
   * @param {number} options.status - HTTP status code (default: 200)
   * @param {string} options.format - Format type (default: 'json')
   * @param {string} options.message - Response message
   * @param {Object} options.meta - Additional metadata
   */
  constructor(data, options = {}) {
    this.data = data;
    this.statusCode = options.status || 200;
    this.formatType = options.format || 'json';
    this.message = options.message || null;
    this.meta = options.meta || null;
    this.timestamp = new Date().toISOString();
  }

  /**
   * Format the response data according to the specified format.
   * This method should be overridden by child classes for specific formatting.
   * @returns {Object} Formatted response object
   */
  format() {
    return this.getFormattedResponse();
  }

  /**
   * Default JSON format
   * @returns {Object} JSON formatted response
   */
  formatJson() {
    const response = {};

    // Add message if provided
    if (this.message) {
      response.message = this.message;
    }

    // Add data if provided
    if (this.data !== null && this.data !== undefined) {
      response.data = this.data;
    }

    // Add meta information if provided
    if (this.meta) {
      response.meta = this.meta;
    }

    // Always add timestamp
    response.timestamp = this.timestamp;

    return response;
  }

  /**
   * Format as plain format (simple structure)
   * @returns {Object} Plain formatted response
   */
  formatPlain() {
    return {
      success: this.statusCode >= 200 && this.statusCode < 300,
      data: this.data,
      message: this.message,
      timestamp: this.timestamp,
    };
  }

  /**
   * Format as detailed format (with additional metadata)
   * @returns {Object} Detailed formatted response
   */
  formatDetailed() {
    const response = this.formatJson();

    response.status = {
      code: this.statusCode,
      success: this.statusCode >= 200 && this.statusCode < 300,
    };

    return response;
  }

  /**
   * Get the appropriate format based on the format type
   * @returns {Object} Formatted response
   */
  getFormattedResponse() {
    switch (this.formatType) {
      case 'plain':
      case 'plainFormat':
        return this.formatPlain();
      case 'detailed':
      case 'detailedFormat':
        return this.formatDetailed();
      case 'json':
      default:
        return this.formatJson();
    }
  }

  /**
   * Static method to create a new output instance.
   * @param {*} data - The data to be formatted
   * @param {Object} options - Options for the response
   * @returns {BaseOutput} Output instance
   */
  static new(data, options = {}) {
    return new this(data, options);
  }
}

module.exports = BaseOutput;
