// Test setup file
require('dotenv').config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test timeout
jest.setTimeout(10000);

// Setup database for tests
beforeAll(async () => {
  // Database setup would go here
  // For now, we'll just ensure the test environment is set
  console.log('Setting up test environment...');
});

afterAll(async () => {
  // Cleanup after all tests
  console.log('Cleaning up test environment...');
});

// Reset database state between tests
beforeEach(async () => {
  // Reset database state if needed
});

afterEach(async () => {
  // Cleanup after each test
  jest.clearAllMocks();
});
