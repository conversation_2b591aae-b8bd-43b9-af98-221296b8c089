const request = require('supertest');
const app = require('../src/app');
const ErrorOutput = require('../src/outputs/ErrorOutput');
const { AuthOutput } = require('../src/outputs/authResponses');

describe('Error Handling Refactoring', () => {
  describe('ErrorOutput Class', () => {
    test('should create error from string message', () => {
      const error = new ErrorOutput('Test error message');
      expect(error.message).toBe('Test error message');
      expect(error.statusCode).toBe(500);
    });

    test('should create error from Error object', () => {
      const originalError = new Error('Original error');
      const error = new ErrorOutput(originalError);
      expect(error.message).toBe('Original error');
      expect(error.originalError).toBe(originalError);
    });

    test('should handle JWT errors with correct status', () => {
      const jwtError = new Error('Invalid token');
      jwtError.name = 'JsonWebTokenError';
      const error = new ErrorOutput(jwtError);
      expect(error.statusCode).toBe(401);
    });

    test('should format error response correctly', () => {
      const error = new ErrorOutput('Test error', {
        status: 400,
        path: '/test',
        method: 'POST'
      });
      
      const formatted = error.format();
      expect(formatted).toHaveProperty('error', 'Test error');
      expect(formatted).toHaveProperty('timestamp');
      expect(formatted).toHaveProperty('path', '/test');
      expect(formatted).toHaveProperty('method', 'POST');
    });

    test('should create validation error using static method', () => {
      const validationResult = {
        errors: { email: 'Email is required' }
      };
      
      const error = ErrorOutput.fromValidation(validationResult);
      expect(error.statusCode).toBe(400);
      expect(error.errors).toEqual({ email: 'Email is required' });
    });

    test('should create auth error using static method', () => {
      const error = ErrorOutput.authError('Invalid credentials');
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('Invalid credentials');
    });
  });

  describe('AuthOutput Class', () => {
    const mockResult = {
      token: 'test-token',
      user: { id: 1, email: '<EMAIL>', role: 'user' }
    };

    test('should create login output with correct format', () => {
      const output = AuthOutput.login(mockResult);
      expect(output.formatType).toBe('login');
      expect(output.statusCode).toBe(200);
      expect(output.message).toBe('Login successful');
    });

    test('should create register output with correct format', () => {
      const output = AuthOutput.register(mockResult);
      expect(output.formatType).toBe('register');
      expect(output.statusCode).toBe(201);
      expect(output.message).toBe('Registration successful');
    });

    test('should format login response correctly', () => {
      const output = AuthOutput.login(mockResult);
      const formatted = output.format();

      expect(formatted).toHaveProperty('data');
      expect(formatted.data).toHaveProperty('token', 'test-token');
      expect(formatted.data).toHaveProperty('user');
      expect(formatted).toHaveProperty('meta');
      expect(formatted.meta).toHaveProperty('action', 'login');
    });

    test('should format register response correctly', () => {
      const output = AuthOutput.register(mockResult);
      const formatted = output.format();

      expect(formatted).toHaveProperty('data');
      expect(formatted.data).toHaveProperty('token', 'test-token');
      expect(formatted.data).toHaveProperty('user');
      expect(formatted).toHaveProperty('meta');
      expect(formatted.meta).toHaveProperty('action', 'register');
      expect(formatted.meta).toHaveProperty('welcome', true);
    });
  });

  describe('Controller Error Handling Integration', () => {
    test('should handle validation errors consistently', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          // Missing required fields
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('errors');
    });

    test('should handle authentication errors consistently', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });

      // Should return either 401 (auth error) or 500 (database error in test env)
      // Both should have consistent error format
      expect([401, 500]).toContain(response.status);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');

      // Verify error format is consistent regardless of status code
      expect(typeof response.body.error).toBe('string');
      expect(typeof response.body.timestamp).toBe('string');
    });

    test('should handle missing token errors consistently', async () => {
      const response = await request(app)
        .post('/api/v1/auth/verify');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error', 'No token provided');
      expect(response.body).toHaveProperty('timestamp');
    });

    test('should handle invalid token errors consistently', async () => {
      const response = await request(app)
        .post('/api/v1/auth/verify')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('Centralized Error Handling', () => {
    test('should catch and format unexpected errors', async () => {
      // This would test that any uncaught errors in controllers
      // are properly handled by the BaseController error handling
      // We'd need to create a test endpoint that throws an error
      // for this to work properly in a real scenario
    });
  });
});
