const LoginInput = require('../src/inputs/LoginInput');
const RegisterInput = require('../src/inputs/RegisterInput');
const { AuthOutput, AuthErrorOutput } = require('../src/outputs/authResponses');

describe('Integration Test - New Pattern Usage', () => {
  describe('Controller Pattern Simulation', () => {
    it('should demonstrate the new login flow', () => {
      // Simulate controller method: AuthController#login
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123'
      };

      // Step 1: Create input instance with request data
      const input = LoginInput.new(requestBody);
      
      // Step 2: Validate input
      const validationResult = input.validate();
      expect(validationResult.success).toBe(true);
      expect(input.output).toEqual(requestBody);

      // Step 3: Simulate service call with validated data
      const serviceResult = {
        token: 'jwt-token-123',
        user: {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user',
          created_at: '2023-01-01T00:00:00.000Z'
        }
      };

      // Step 4: Create output with service result
      const output = AuthOutput.login(serviceResult, {
        status: 200,
        format: 'json'
      });

      // Step 5: Verify output formatting
      const formattedResponse = output.format();
      expect(formattedResponse).toEqual({
        message: 'Login successful',
        data: {
          token: 'jwt-token-123',
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            role: 'user',
            created_at: '2023-01-01T00:00:00.000Z'
          }
        },
        timestamp: expect.any(String)
      });
      expect(output.statusCode).toBe(200);
    });

    it('should demonstrate the new login flow with validation error', () => {
      // Simulate controller method with invalid data
      const requestBody = {
        email: 'invalid-email',
        password: 'password123'
      };

      // Step 1: Create input instance with request data
      const input = LoginInput.new(requestBody);
      
      // Step 2: Validate input (should fail)
      const validationResult = input.validate();
      expect(validationResult.success).toBe(false);
      expect(validationResult.errors).toHaveProperty('email');
      expect(input.output).toBeNull();

      // Step 3: Create error output for validation failure
      const errorOutput = new AuthErrorOutput('Validation failed', { status: 400 });
      const formattedResponse = errorOutput.format();
      
      expect(formattedResponse).toEqual({
        error: 'Validation failed',
        timestamp: expect.any(String)
      });
      expect(errorOutput.statusCode).toBe(400);
    });

    it('should demonstrate the new register flow', () => {
      // Simulate controller method: AuthController#register
      const requestBody = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        role: 'user'
      };

      // Step 1: Create input instance with request data
      const input = RegisterInput.new(requestBody);
      
      // Step 2: Validate input
      const validationResult = input.validate();
      expect(validationResult.success).toBe(true);
      expect(input.output).toEqual(requestBody);

      // Step 3: Simulate service call with validated data
      const serviceResult = {
        token: 'jwt-token-456',
        user: {
          id: 2,
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'user',
          created_at: '2023-01-01T00:00:00.000Z'
        }
      };

      // Step 4: Create output with service result
      const output = AuthOutput.register(serviceResult, {
        status: 201,
        format: 'plainFormat'
      });

      // Step 5: Verify output formatting (plain format)
      const formattedResponse = output.format();
      expect(formattedResponse).toEqual({
        success: true,
        data: {
          token: 'jwt-token-456',
          user: {
            id: 2,
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'user',
            created_at: '2023-01-01T00:00:00.000Z'
          }
        },
        message: 'Registration successful',
        timestamp: expect.any(String)
      });
      expect(output.statusCode).toBe(201);
    });

    it('should demonstrate service error handling', () => {
      // Simulate controller method with valid input but service error
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123'
      };

      // Step 1: Create and validate input (success)
      const input = LoginInput.new(requestBody);
      const validationResult = input.validate();
      expect(validationResult.success).toBe(true);

      // Step 2: Simulate service error (e.g., user not found)
      const serviceError = {
        success: false,
        message: 'Invalid email or password',
        statusCode: 401
      };

      // Step 3: Create error output for service failure
      const errorOutput = new AuthErrorOutput(serviceError.message, { 
        status: serviceError.statusCode 
      });
      
      const formattedResponse = errorOutput.format();
      expect(formattedResponse).toEqual({
        error: 'Invalid email or password',
        timestamp: expect.any(String)
      });
      expect(errorOutput.statusCode).toBe(401);
    });

    it('should demonstrate different output formats', () => {
      const serviceResult = {
        token: 'jwt-token-789',
        user: {
          id: 3,
          name: 'Jane Doe',
          email: '<EMAIL>',
          role: 'admin',
          created_at: '2023-01-01T00:00:00.000Z'
        }
      };

      // Test JSON format (default)
      const jsonOutput = AuthOutput.login(serviceResult, { format: 'json' });
      const jsonResponse = jsonOutput.format();
      expect(jsonResponse).toHaveProperty('message');
      expect(jsonResponse).toHaveProperty('data');
      expect(jsonResponse).toHaveProperty('timestamp');

      // Test plain format
      const plainOutput = AuthOutput.login(serviceResult, { format: 'plainFormat' });
      const plainResponse = plainOutput.format();
      expect(plainResponse).toHaveProperty('success', true);
      expect(plainResponse).toHaveProperty('data');
      expect(plainResponse).toHaveProperty('message');
      expect(plainResponse).toHaveProperty('timestamp');

      // Test detailed format
      const detailedOutput = AuthOutput.login(serviceResult, { format: 'detailedFormat' });
      const detailedResponse = detailedOutput.format();
      expect(detailedResponse).toHaveProperty('message');
      expect(detailedResponse).toHaveProperty('data');
      expect(detailedResponse).toHaveProperty('timestamp');
      expect(detailedResponse).toHaveProperty('status');
      expect(detailedResponse.status).toEqual({
        code: 200,
        success: true
      });
    });
  });
});
