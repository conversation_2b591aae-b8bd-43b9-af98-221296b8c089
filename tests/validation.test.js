const BaseInput = require('../src/inputs/BaseInput');
const LoginInput = require('../src/inputs/LoginInput');
const RegisterInput = require('../src/inputs/RegisterInput');

describe('Input Validation Classes', () => {
  describe('BaseInput', () => {
    it('should throw error when getSchema is not implemented', () => {
      expect(() => {
        new BaseInput();
      }).toThrow('getSchema() must be implemented by child classes');
    });

    it('should create middleware using static method', () => {
      const middleware = BaseInput.createMiddleware(LoginInput);
      expect(typeof middleware).toBe('function');
    });
  });

  describe('LoginInput', () => {
    let loginInput;

    beforeEach(() => {
      loginInput = new LoginInput();
    });

    it('should validate correct login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = loginInput.validate(validData);
      expect(result.success).toBe(true);
      expect(result.errors).toBeNull();
    });

    it('should reject invalid email format', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'password123',
      };

      const result = loginInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('email');
    });

    it('should reject missing email', () => {
      const invalidData = {
        password: 'password123',
      };

      const result = loginInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('email');
    });

    it('should reject missing password', () => {
      const invalidData = {
        email: '<EMAIL>',
      };

      const result = loginInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('password');
    });

    it('should reject additional properties', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'password123',
        extraField: 'not allowed',
      };

      const result = loginInput.validate(invalidData);
      expect(result.success).toBe(false);
    });

    it('should create working middleware', () => {
      const middleware = loginInput.getMiddleware();
      expect(typeof middleware).toBe('function');

      const mockReq = {
        body: {
          email: '<EMAIL>',
          password: 'password123',
        },
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      const mockNext = jest.fn();

      middleware(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return error response for invalid data in middleware', () => {
      const middleware = loginInput.getMiddleware();
      
      const mockReq = {
        body: {
          email: 'invalid-email',
          password: 'password123',
        },
      };
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      const mockNext = jest.fn();

      middleware(mockReq, mockRes, mockNext);
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Validation Error',
          message: 'Invalid input data',
          errors: expect.any(Object),
          timestamp: expect.any(String),
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('RegisterInput', () => {
    let registerInput;

    beforeEach(() => {
      registerInput = new RegisterInput();
    });

    it('should validate correct registration data', () => {
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        role: 'user',
      };

      const result = registerInput.validate(validData);
      expect(result.success).toBe(true);
      expect(result.errors).toBeNull();
    });

    it('should validate registration data without role (optional field)', () => {
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = registerInput.validate(validData);
      expect(result.success).toBe(true);
      expect(result.errors).toBeNull();
    });

    it('should reject invalid name with numbers', () => {
      const invalidData = {
        name: 'John123',
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = registerInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('name');
    });

    it('should reject short name', () => {
      const invalidData = {
        name: 'J',
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = registerInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('name');
    });

    it('should reject weak password', () => {
      const invalidData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'weak',
      };

      const result = registerInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('password');
    });

    it('should reject invalid role', () => {
      const invalidData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        role: 'invalid_role',
      };

      const result = registerInput.validate(invalidData);
      expect(result.success).toBe(false);
      expect(result.errors).toHaveProperty('role');
    });

    it('should accept valid roles', () => {
      const roles = ['admin', 'user', 'moderator'];
      
      roles.forEach(role => {
        const validData = {
          name: 'John Doe',
          email: '<EMAIL>',
          password: 'Password123!',
          role: role,
        };

        const result = registerInput.validate(validData);
        expect(result.success).toBe(true);
      });
    });
  });
});
