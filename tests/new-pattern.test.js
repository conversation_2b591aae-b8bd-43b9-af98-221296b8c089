const BaseInput = require('../src/inputs/BaseInput');
const LoginInput = require('../src/inputs/LoginInput');
const RegisterInput = require('../src/inputs/RegisterInput');
const BaseController = require('../src/controllers/BaseController');
const BaseOutput = require('../src/outputs/BaseOutput');
const { AuthOutput, AuthErrorOutput } = require('../src/outputs/authResponses');

describe('New Pattern Implementation', () => {
  describe('Input Classes with New Pattern', () => {
    describe('LoginInput', () => {
      it('should create instance with data using new() method', () => {
        const data = { email: '<EMAIL>', password: 'password123' };
        const input = LoginInput.new(data);
        
        expect(input).toBeInstanceOf(LoginInput);
        expect(input.data).toEqual(data);
      });

      it('should validate and set output property on success', () => {
        const data = { email: '<EMAIL>', password: 'password123' };
        const input = LoginInput.new(data);
        
        const result = input.validate();
        
        expect(result.success).toBe(true);
        expect(input.output).toEqual(data);
      });

      it('should not set output property on validation failure', () => {
        const data = { email: 'invalid-email', password: 'password123' };
        const input = LoginInput.new(data);
        
        const result = input.validate();
        
        expect(result.success).toBe(false);
        expect(input.output).toBeNull();
      });

      it('should throw error when validating without data', () => {
        const input = new LoginInput();
        
        expect(() => input.validate()).toThrow('No data provided for validation');
      });
    });

    describe('RegisterInput', () => {
      it('should create instance with data using new() method', () => {
        const data = { 
          name: 'John Doe', 
          email: '<EMAIL>', 
          password: 'Password123!' 
        };
        const input = RegisterInput.new(data);
        
        expect(input).toBeInstanceOf(RegisterInput);
        expect(input.data).toEqual(data);
      });

      it('should validate and set output property on success', () => {
        const data = { 
          name: 'John Doe', 
          email: '<EMAIL>', 
          password: 'Password123!' 
        };
        const input = RegisterInput.new(data);
        
        const result = input.validate();
        
        expect(result.success).toBe(true);
        expect(input.output).toEqual(data);
      });
    });
  });

  describe('BaseController', () => {
    let mockRes;

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        headersSent: false,
      };
    });

    describe('validateInput', () => {
      it('should return true for valid input', () => {
        const data = { email: '<EMAIL>', password: 'password123' };
        const input = LoginInput.new(data);
        input.validate(); // Pre-validate to set success state
        
        const result = BaseController.validateInput(input, mockRes);
        
        expect(result).toBe(true);
        expect(mockRes.status).not.toHaveBeenCalled();
      });

      it('should return false and send error response for invalid input', () => {
        const data = { email: 'invalid-email', password: 'password123' };
        const input = LoginInput.new(data);
        
        const result = BaseController.validateInput(input, mockRes);
        
        expect(result).toBe(false);
        expect(mockRes.status).toHaveBeenCalledWith(400);
        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            error: 'Validation Error',
            errors: expect.any(Object),
            timestamp: expect.any(String),
          })
        );
      });
    });

    describe('sendResponse', () => {
      it('should send response using output instance', () => {
        const result = { token: 'test-token', user: { id: 1, email: '<EMAIL>' } };
        const output = AuthOutput.login(result);

        BaseController.sendResponse(output, mockRes);

        expect(mockRes.status).toHaveBeenCalledWith(200);
        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Login successful',
            data: expect.objectContaining({
              token: 'test-token',
              user: expect.any(Object)
            }),
            timestamp: expect.any(String),
          })
        );
      });
    });

    describe('handleError', () => {
      it('should send generic error response', () => {
        const error = new Error('Test error');
        const mockNext = jest.fn();
        
        BaseController.handleError(error, mockRes, mockNext);
        
        expect(mockRes.status).toHaveBeenCalledWith(500);
        expect(mockRes.json).toHaveBeenCalledWith(
          expect.objectContaining({
            error: 'Test error',
            timestamp: expect.any(String),
          })
        );
      });

      it('should call next if headers already sent', () => {
        const error = new Error('Test error');
        const mockNext = jest.fn();
        mockRes.headersSent = true;
        
        BaseController.handleError(error, mockRes, mockNext);
        
        expect(mockNext).toHaveBeenCalledWith(error);
        expect(mockRes.status).not.toHaveBeenCalled();
      });
    });
  });

  describe('Output Classes', () => {
    describe('BaseOutput', () => {
      it('should create instance with data and options', () => {
        const data = { test: 'data' };
        const options = { status: 201, message: 'Created', format: 'json' };
        const output = new BaseOutput(data, options);
        
        expect(output.data).toEqual(data);
        expect(output.statusCode).toBe(201);
        expect(output.message).toBe('Created');
        expect(output.formatType).toBe('json');
      });

      it('should format response correctly', () => {
        const data = { test: 'data' };
        const options = { message: 'Success' };
        const output = new BaseOutput(data, options);
        
        const formatted = output.format();
        
        expect(formatted).toEqual({
          message: 'Success',
          data: data,
          timestamp: expect.any(String),
        });
      });

      it('should format plain response correctly', () => {
        const data = { test: 'data' };
        const options = { message: 'Success', format: 'plain' };
        const output = new BaseOutput(data, options);
        
        const formatted = output.format();
        
        expect(formatted).toEqual({
          success: true,
          data: data,
          message: 'Success',
          timestamp: expect.any(String),
        });
      });
    });

    describe('AuthOutput', () => {
      it('should format login response correctly', () => {
        const result = {
          token: 'test-token',
          user: { id: 1, name: 'Test User', email: '<EMAIL>', role: 'user', created_at: '2023-01-01' }
        };
        const output = AuthOutput.login(result);
        
        const formatted = output.format();
        
        expect(formatted).toEqual({
          message: 'Login successful',
          data: {
            token: 'test-token',
            user: {
              id: 1,
              name: 'Test User',
              email: '<EMAIL>',
              role: 'user',
              created_at: '2023-01-01'
            }
          },
          meta: {
            action: 'login',
          },
          timestamp: expect.any(String),
        });
        expect(output.statusCode).toBe(200);
      });
    });

    describe('AuthErrorOutput', () => {
      it('should format error response correctly', () => {
        const output = new AuthErrorOutput('Authentication failed', { status: 401 });
        
        const formatted = output.format();
        
        expect(formatted).toEqual({
          error: 'Authentication failed',
          timestamp: expect.any(String),
        });
        expect(output.statusCode).toBe(401);
      });
    });
  });
});
