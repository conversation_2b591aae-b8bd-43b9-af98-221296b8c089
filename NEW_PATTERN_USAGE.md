# New Pattern Usage Guide

This document demonstrates the new refactored pattern for handling input validation and output formatting in controllers.

## Overview

The refactoring moves input validation from middleware into controller methods and introduces a structured output system with base classes.

## Pattern Structure

### 1. Input Validation in Controllers

**Before (Middleware Pattern):**
```javascript
// In routes/auth.js
router.post('/login', validateLoginMiddleware, AuthController.login);

// In controller
static async login(req, res, next) {
  // Input is already validated by middleware
  const { email, password } = req.body;
  // ... rest of logic
}
```

**After (Controller Pattern):**
```javascript
// In routes/auth.js
router.post('/login', AuthController.login);

// In controller
static async login(req, res, next) {
  // Step 1: Create input instance and validate
  const input = LoginInput.new(req.body);
  if (!this.validateInput(input, res)) {
    return;
  }

  // Step 2: Use validated data
  const result = await AuthService.authenticateUser(input.output.email, input.output.password);

  // Step 3: Format and send response
  if (!result.success) {
    const errorOutput = new AuthErrorOutput(result.message, { status: result.statusCode });
    return this.sendResponse(errorOutput, res);
  }

  const output = new LoginOutput(result.data, { status: result.statusCode });
  this.sendResponse(output, res);
}
```

### 2. Input Classes

Input classes now support instantiation with data:

```javascript
// Create input instance with data
const input = LoginInput.new(requestData);

// Validate the data
const validationResult = input.validate();

if (validationResult.success) {
  // Access validated data through input.output
  console.log(input.output); // Contains the validated data
} else {
  // Handle validation errors
  console.log(validationResult.errors);
}
```

### 3. Output Classes

Output classes provide structured response formatting:

```javascript
// Basic usage
const output = new LoginOutput(serviceResult, {
  status: 200,
  format: 'json'
});

// Different formats
const plainOutput = new LoginOutput(serviceResult, { format: 'plainFormat' });
const detailedOutput = new LoginOutput(serviceResult, { format: 'detailedFormat' });

// Send response
this.sendResponse(output, res);
```

## Complete Example

Here's a complete example of the new pattern in action:

```javascript
// AuthController.js
const BaseController = require('./BaseController');
const LoginInput = require('../inputs/LoginInput');
const { LoginOutput, AuthErrorOutput } = require('../outputs/authResponses');

class AuthController extends BaseController {
  static async login(req, res, next) {
    try {
      // Step 1: Validate input
      const input = LoginInput.new(req.body);
      if (!this.validateInput(input, res)) {
        return;
      }

      // Step 2: Call service with validated data
      const result = await AuthService.authenticateUser(
        input.output.email, 
        input.output.password
      );

      // Step 3: Handle service response
      if (!result.success) {
        const errorOutput = new AuthErrorOutput(result.message, { 
          status: result.statusCode 
        });
        return this.sendResponse(errorOutput, res);
      }

      // Step 4: Send success response
      const output = new LoginOutput(result.data, { 
        status: result.statusCode,
        format: 'json' // or 'plainFormat', 'detailedFormat'
      });
      this.sendResponse(output, res);
    } catch (error) {
      this.handleError(error, res, next);
    }
  }
}
```

## Benefits

1. **Centralized Validation**: Input validation happens in controllers where business logic resides
2. **Type Safety**: `input.output` contains validated data with proper structure
3. **Consistent Responses**: Output classes ensure consistent response formatting
4. **Flexible Formatting**: Support for different response formats (json, plain, detailed)
5. **Better Error Handling**: Structured error responses through base classes
6. **Maintainability**: Clear separation of concerns with base classes

## Migration Guide

To migrate existing controllers:

1. Remove validation middleware from routes
2. Add input validation at the start of controller methods
3. Replace manual response formatting with output classes
4. Extend BaseController for common functionality

## Output Formats

### JSON Format (Default)
```json
{
  "message": "Login successful",
  "data": { "token": "...", "user": {...} },
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```

### Plain Format
```json
{
  "success": true,
  "data": { "token": "...", "user": {...} },
  "message": "Login successful",
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```

### Detailed Format
```json
{
  "message": "Login successful",
  "data": { "token": "...", "user": {...} },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "status": {
    "code": 200,
    "success": true
  }
}
```
