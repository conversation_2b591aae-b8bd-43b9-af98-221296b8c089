# Simple Express API

A complete Express.js starter project with authentication, validation, testing, and best practices.

## Features

- **Express.js** - Fast, unopinionated web framework
- **Sequelize ORM** - PostgreSQL database integration
- **JWT Authentication** - Secure token-based authentication
- **Input Validation** - AJV schema validation
- **Password Hashing** - bcrypt for secure password storage
- **Error Handling** - Comprehensive error handling middleware
- **Security Headers** - Security best practices implemented
- **Testing** - Jest testing framework with coverage
- **Linting** - ESLint with recommended rules
- **Database Migrations** - Sequelize CLI for database management

## Project Structure

```
/src/
  /config/          # Database and application configuration
  /controllers/     # Route controllers
  /inputs/          # Input validation schemas
  /middlewares/     # Custom middleware functions
  /migrations/      # Database migrations
  /models/          # Sequelize models
  /outputs/         # Response formatters
  /routes/          # API routes
  /services/        # Business logic services
  /utils/           # Utility functions
  app.js            # Express application setup
  server.js         # Server startup
/tests/             # Test files
.env                # Environment variables
.gitignore          # Git ignore rules
package.json        # Project dependencies and scripts
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd simple-express-api
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

4. Set up the database:
```bash
# Create database
createdb simple_express_api_development

# Run migrations
npm run db:migrate
```

5. Start the development server:
```bash
npm run dev
```

The server will start on `http://localhost:3000`

## API Endpoints

### Authentication

- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/verify` - Token verification
- `POST /api/v1/auth/logout` - User logout

### Health Check

- `GET /health` - Application health status

## Testing

Run tests:
```bash
npm test
```

Run tests with coverage:
```bash
npm run test:coverage
```

Run tests in watch mode:
```bash
npm run test:watch
```

## Linting

Run ESLint:
```bash
npm run lint
```

Fix linting issues:
```bash
npm run lint:fix
```

## Database Management

Run migrations:
```bash
npm run db:migrate
```

Rollback migrations:
```bash
npm run db:migrate:undo
```

Reset database:
```bash
npm run db:reset
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | `localhost` |
| `DB_PORT` | Database port | `5432` |
| `DB_NAME` | Database name | `simple_express_api_development` |
| `DB_USERNAME` | Database username | `postgres` |
| `DB_PASSWORD` | Database password | `password` |
| `JWT_SECRET` | JWT secret key | Required |
| `JWT_EXPIRES_IN` | JWT expiration time | `24h` |
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment | `development` |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the MIT License.
