{"name": "simple-express-api", "version": "1.0.0", "description": "A complete Express.js starter project with authentication, validation, and testing", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:migrate:undo:all && sequelize-cli db:migrate && sequelize-cli db:seed:all"}, "keywords": ["express", "nodejs", "api", "authentication", "jwt", "sequelize", "postgresql", "validation", "testing"], "author": "Your Name", "license": "MIT", "type": "commonjs", "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "sequelize": "^6.37.7"}, "devDependencies": {"eslint": "^9.32.0", "jest": "^30.0.5", "jest-junit": "^16.0.0", "nodemon": "^3.1.10", "sequelize-cli": "^6.6.3", "supertest": "^7.1.4"}}